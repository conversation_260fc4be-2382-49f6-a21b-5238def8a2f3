import requests
import json

proxies = {
    "http": "http://127.0.0.1:7897",
    "https": "http://127.0.0.1:7897"
}

headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "content-type": "application/json",
    "ok-verify-sign": "pJueFkfWvTxEZZp6CnrX4CtQp74D9PXcXumw2fk4mhw=",
    "ok-verify-token": "bce79b94-4dff-433b-bc26-08cfdb387665",
    "origin": "https://web3.okx.com",
    "priority": "u=1, i",
    "referer": "https://web3.okx.com/zh-hans/token/solana/6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-utc": "8",
    "x-zkdex-env": "0"
}
cookies = {
    "_gcl_gs": "2.1.k1$i1747121212$u3801134",
    "intercom-id-ny9cf50h": "59b1a03b-f342-4a22-9469-ca04541cfc98",
    "intercom-device-id-ny9cf50h": "e3eb8110-ee9e-4eec-8777-a8840b95118e",
    "_ym_uid": "1747121226975031991",
    "_ym_d": "1747121226",
    "devId": "ed72f275-eb0a-418a-be47-91f58cf52649",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "ok_login_type": "OKX_GLOBAL",
    "amp_21c676": "sxXY9NatdSUAkrG8DFaDSs.ZDVtenN3VUNMVkowT3d6K1hZU0RnQT09..1is8n1mkg.1is8pnra8.i.f.11",
    "OptanonAlertBoxClosed": "2025-05-29T05:32:26.912Z",
    "ok_prefer_currency": "0%7C1%7Cfalse%7CUSD%7C2%7C%24%7C1%7C1%7C%E7%BE%8E%E5%85%83",
    "ok_global": "{%22g_t%22:2}",
    "fingerprint_id": "286c47e1-4945-45bb-88e9-98ddc1deff0f",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "mse": "0",
    "_gcl_au": "1.1.1811190387.1753323315",
    "OptanonConsent": "isGpcEnabled=0&datestamp=Mon+Jul+28+2025+09%3A43%3A27+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&landingPath=NotLandingPage&groups=C0004%3A0%2CC0002%3A0%2CC0003%3A0%2CC0001%3A1&geolocation=US%3B&AwaitingReconsent=false",
    "intercom-session-ny9cf50h": "a0ZDbFNldWc0WWozRWhtdlJIM3poSnpnQmdOaHVEVG92ckZsMjUyNzdNbWNnamhyZmRUSW9aNGcxTERYb2d0czNKS1F6a053cFlDN2l1bExQUTF3L1ozbW9QWFJVS1Q1djlBQk9VaVhRcjQ9LS02TW1MOUFWNjFJOWh1NnZwMi9wWTR3PT0=--6ea58f3a858b8b31ca4d246a6ca003de7f5fe61a",
    "__cf_bm": "gi02QNmdNKMYYhczntU2vRExt_Oh5gr0WuEXZOnmbJg-1754036335-*******-Mlgu.r6_AhhLUj0xKfkYLUW.a4geT4V9Rbw_lMFyjVBsnicbXfIqWrEKmkXHUwchVNFQlflTvDnb0RveyTEuick1YwHOCBRVEbAKb56Ciew",
    "_gid": "GA1.2.404226676.1754036338",
    "_ym_isad": "2",
    "_ym_visorc": "b",
    "ok_site_info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye",
    "ok-exp-time": "1754036402307",
    "tmx_session_id": "v2ddxkgvwi_1754036404492",
    "fp_s": "0",
    "okg.currentMedia": "xl",
    "traceId": "2120940367767790006",
    "_ga": "GA1.1.2028963803.1747121216",
    "_ga_G0EKWWQGTZ": "GS2.1.s1754036338$o173$g1$t1754036779$j58$l0$h0",
    "_monitor_extras": "{\"deviceId\":\"OWTCklx6Hw5J0KFQPhe8Aq\",\"eventId\":2633,\"sequenceNumber\":2633}",
    "ok-ses-id": "xRWrmE4Qtt9df+xCb4o8dQwsWWKMV/cm9mPrB0IZ/iuxKGHzVcgMGLPwbQAOODejyfa66+r+o//hq29TSUA9F5O5yBaNYXD/CIQcCibwtlIUWL7D24ETz1kU9keHG52H"
}
url = "https://web3.okx.com/priapi/v1/dx/market/v2/trading-history/filter-list"
params = {
    "t": "1754036848561"
}
data = {
    "desc": True,
    "orderBy": "timestamp",
    "limit": 100,
    "tradingHistoryFilter": {
        "chainId": "501",
        "tokenContractAddress": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN",
        "type": "0",
        "currentUserWalletAddress": "",
        "userAddressList": [],
        "volumeMin": "",
        "volumeMax": "",
        "priceMin": "",
        "priceMax": "",
        "startTime": 1753891200000,
        "endTime": 1753894200000
    }
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies,  data=data, proxies=proxies)

# 检查请求是否成功
if response.status_code == 200:
    try:
        # 解析JSON响应
        json_data = response.json()

        # 打印完整响应（可选，用于调试）
        print("=== 完整响应 ===")
        print(json.dumps(json_data, indent=2, ensure_ascii=False))
        print("\n" + "="*50 + "\n")

        # 检查响应结构
        if json_data.get("code") == 0 and "data" in json_data:
            data = json_data["data"]

            if "list" in data and isinstance(data["list"], list):
                dex_list = data["list"]
                print(f"找到 {len(dex_list)} 条交易记录")
                print("\n=== 所有 dexName 字段 ===")

                # 提取所有dexName（用于统计）
                all_dex_names = []
                unique_dex_names = []  # 用于去重显示

                print("=== 所有 dexName（去重显示）===")
                for i, item in enumerate(dex_list):
                    if "dexName" in item:
                        dex_name = item["dexName"]
                        all_dex_names.append(dex_name)

                        # 只有第一次出现时才打印
                        if dex_name not in unique_dex_names:
                            unique_dex_names.append(dex_name)
                            print(f"发现新的 dexName: {dex_name}")
                    else:
                        print(f"第 {i+1} 条记录没有 dexName 字段")

                # 统计信息
                if all_dex_names:
                    print(f"\n=== dexName 统计 ===")
                    print(f"总共找到 {len(all_dex_names)} 个 dexName")
                    print(f"去重后有 {len(unique_dex_names)} 个不同的 dexName")

                    print(f"\n=== 去重后的 dexName 列表 ===")
                    for i, dex_name in enumerate(unique_dex_names, 1):
                        count = all_dex_names.count(dex_name)
                        print(f"{i}. {dex_name} (出现 {count} 次)")
                else:
                    print("没有找到任何 dexName 字段")
            else:
                print("响应中没有找到 list 数组或 list 不是数组类型")
        else:
            print(f"API返回错误，code: {json_data.get('code')}")
            print(f"响应内容: {json_data}")

    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"响应内容: {response.text}")

else:
    print(f"请求失败，状态码: {response.status_code}")
    print(f"响应内容: {response.text}")