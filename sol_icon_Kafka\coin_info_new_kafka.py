#!/usr/bin/env python312
"""
SOL新币Kafka消费者 - 生产版本
实时消费Kafka消息，处理新币数据并更新数据库，并将结果写回Kafka
"""

import json
import time
import signal
import sys
import re
import pymysql
import boto3
import redis
import threading
import traceback
from threading import Lock
from queue import Queue
from kafka import KafkaConsumer, KafkaProducer
from kafka.errors import KafkaError
from loguru import logger
from datetime import datetime
from urllib.parse import urlparse
from curl_cffi import requests as cf_requests


class SolKafkaProcessor:
    def __init__(self, num_threads=12):
        """初始化Kafka消费者/生产者和数据处理配置"""

        # Kafka配置
        self.bootstrap_servers = [
            'node1:9092', 'node2:9092', 'node3:9092', 'node4:9092', 'node5:9092',
            'node6:9092', 'node7:9092', 'node8:9092', 'node9:9092', 'node10:9092'
        ]
        self.consumer_topic = 'test-sol-new-coin-data'
        self.producer_topic = 'spider-new-token-icon'
        # 使用固定的消费者组ID，实现真正的多线程负载均衡
        self.consumer_group = 'sol-coin-spider-group-liujunyi'
        logger.info(f"消费者组ID: {self.consumer_group} (多线程负载均衡消费)")

        # 数据库配置
        self.db_config = {
            'host': '**********',
            'port': 6000,
            'user': 'root',
            'password': 'iAn7*+154-j9r3_dcm',
            'database': 'solana'
        }

        # AWS S3配置
        self.aws_config = {
            'aws_access_key_id': '********************',
            'aws_secret_access_key': 'Jax967rCDcH8uVdp3EQzd+wRR1NEm56IJgJUrWRn',
            'region_name': 'ap-northeast-1'
        }
        self.bucket_name = 'chainsight-dex-spider'
        # 为单线程模式(如测试)和作为备用而初始化
        self.s3 = boto3.client('s3', **self.aws_config)

        # Redis配置（用于失败记录）
        self.redis_config = {
            'host': '**************',
            'password': 123456,
            'port': 6379,
            'db': 4,
            'decode_responses': True
        }
        self.redis_client = redis.Redis(**self.redis_config)
        self.failed_url_key = 'sol_kafka:failed_url'
        self.failed_image_key = 'sol_kafka:failed_image'
        self.failed_producer_key = 'sol_kafka:failed_producer'

        # 请求配置
        self.max_retry = 3
        self.timeout = 10

        # 代理池配置 - 轮换使用
        self.proxy_pool = [
            {
                "http": "http://acf9782b22eb30:<EMAIL>:5001",
                "https": "http://acf9782b22eb30:<EMAIL>:5001"
            },
            {
                "http": "http://335d342ccf3595:<EMAIL>:5001",
                "https": "http://335d342ccf3595:<EMAIL>:5001"
            },
            {
                "http": "http://918f7a889b014b:<EMAIL>:5001",
                "https": "http://918f7a889b014b:<EMAIL>:5001"
            },
            {
                "http": "http://76db578ef195d6:<EMAIL>:5001",
                "https": "http://76db578ef195d6:<EMAIL>:5001"
            },
            {
                "http": "http://85f2c77ca571dc:<EMAIL>:5001",
                "https": "http://85f2c77ca571dc:<EMAIL>:5001"
            },
            {
                "http": "http://bda5cf3b2024b5:<EMAIL>:5001",
                "https": "http://bda5cf3b2024b5:<EMAIL>:5001"
            }
        ]
        self.current_proxy_index = 0
        logger.info(f"代理池已配置 {len(self.proxy_pool)} 个代理，支持自动轮换")

        # 统计信息
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.producer_success_count = 0
        self.producer_error_count = 0
        self.start_time = None

        # 多线程配置
        self.num_threads = num_threads
        self.is_running = True
        self.proxy_lock = Lock()
        self.stats_lock = Lock()

        # 消息队列：消费者线程将消息放入队列，处理线程从队列取消息
        self.message_queue = Queue(maxsize=1000)  # 最大1000条消息缓存
        logger.info(f"消息队列初始化完成，最大缓存: 1000条消息")

        # 初始化Kafka生产者 (线程安全，可共享)
        self.init_kafka_producer()

    def init_kafka_producer(self):
        """初始化Kafka生产者"""
        try:
            producer_config = {
                'bootstrap_servers': self.bootstrap_servers,
                'value_serializer': lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
                'acks': 'all',
                'retries': 3,
                'max_in_flight_requests_per_connection': 1,
                'enable_idempotence': True,
                'request_timeout_ms': 30000,
                'retry_backoff_ms': 1000
            }
            self.producer = KafkaProducer(**producer_config)
            logger.info("✓ Kafka生产者初始化成功")
        except Exception as e:
            logger.error(f"Kafka生产者初始化失败: {e}")
            raise

    def setup_signal_handlers(self):
        """设置信号处理器，退出"""

        def signal_handler(signum, frame):
            logger.warning(f"接收到信号 {signum}，正在安全退出...")
            self.is_running = False
            # 关闭生产者
            if hasattr(self, 'producer'):
                self.producer.close()
            self.show_final_statistics()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 程序终止

    def get_next_proxy(self):
        """获取下一个代理（轮换使用，线程安全）"""
        with self.proxy_lock:
            proxy = self.proxy_pool[self.current_proxy_index]
            current_index = self.current_proxy_index
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_pool)
            logger.debug(f"使用代理 #{current_index + 1}: {proxy['http']}")
            return proxy

    def get_database_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def make_request(self, url, proxy=None):
        """发送HTTP请求，支持重试和IPFS域名转换"""
        if proxy is None:
            proxy = self.get_next_proxy()

        retry_count = 0
        original_url = url  # 保存原始URL

        while retry_count < self.max_retry:
            try:
                retry_count += 1
                logger.debug(f"请求URL (第{retry_count}次): {url}")

                response = cf_requests.get(url, proxies=proxy, impersonate="chrome116", timeout=self.timeout)
                response.raise_for_status()

                try:
                    return response.json()
                except:
                    return response

            except Exception as e:
                if retry_count < self.max_retry:
                    time.sleep(0.5)
                else:
                    # 最后一次重试失败，尝试IPFS域名转换
                    if self.is_ipfs_url(original_url):
                        logger.info(f"检测到IPFS链接，尝试域名转换: {original_url}")
                        converted_result = self.process_ipfs_url(original_url)

                        if converted_result and converted_result['new_url'] != original_url:
                            new_url = converted_result['new_url']
                            logger.info(f"IPFS域名转换成功: {original_url} -> {new_url}")

                            # 用转换后的URL重新请求
                            try:
                                logger.debug(f"使用转换后的URL请求: {new_url}")
                                response = cf_requests.get(new_url, proxies=proxy, impersonate="chrome116",
                                                           timeout=self.timeout)
                                response.raise_for_status()

                                logger.info(f"IPFS域名转换后请求成功: {new_url}")
                                try:
                                    return response.json()
                                except:
                                    return response

                            except Exception as convert_error:
                                logger.error(f"转换后的URL仍然请求失败: {new_url} - 错误: {convert_error}")
                        else:
                            logger.warning(f"IPFS域名转换失败或无需转换: {original_url}")

                    # 记录最终失败
                    self._handle_request_failure(original_url, str(e))
                    return None
        return None

    def is_ipfs_url(self, url):
        """检查URL是否包含ipfs相关域名"""
        url_lower = url.lower()
        return 'ipfs' in url_lower

    def process_ipfs_url(self, url):
        """处理IPFS URL，按优先级尝试不同的方法提取address和替换URL"""
        try:
            logger.debug(f"开始处理IPFS URL: {url}")

            # 方法1：处理 cf-ipfs.com
            result = self.try_cf_ipfs_method(url)
            if result:
                logger.debug(f"使用cf-ipfs方法成功处理")
                return result

            # 方法2：处理 cloudflare-ipfs.com
            result = self.try_cloudflare_ipfs_method(url)
            if result:
                logger.debug(f"使用cloudflare-ipfs方法成功处理")
                return result

            # 方法3：处理 quicknode-ipfs.com
            result = self.try_quicknode_ipfs_method(url)
            if result:
                logger.debug(f"使用quicknode-ipfs方法成功处理")
                return result

            # 方法4：万能方法 - 处理所有包含ipfs的域名
            result = self.try_universal_ipfs_method(url)
            if result:
                logger.debug(f"使用万能IPFS方法成功处理")
                return result

            logger.debug(f"所有方法都无法处理该URL: {url}")
            return None

        except Exception as e:
            logger.error(f"处理IPFS URL时出错: {url} - 错误: {e}")
            return None

    def try_cf_ipfs_method(self, url):
        """方法1：处理cf-ipfs.com格式的URL"""
        try:
            if 'cf-ipfs.com' not in url.lower():
                return None

            # 提取IPFS哈希
            pattern = r'cf-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}

            return None
        except Exception as e:
            logger.error(f"cf-ipfs方法处理失败: {e}")
            return None

    def try_cloudflare_ipfs_method(self, url):
        """方法2：处理cloudflare-ipfs.com格式的URL"""
        try:
            if 'cloudflare-ipfs.com' not in url.lower():
                return None

            # 提取IPFS哈希
            pattern = r'cloudflare-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}

            return None
        except Exception as e:
            logger.error(f"cloudflare-ipfs方法处理失败: {e}")
            return None

    def try_quicknode_ipfs_method(self, url):
        """方法3：处理quicknode-ipfs.com格式的URL"""
        try:
            if 'quicknode-ipfs.com' not in url.lower():
                return None

            # 提取IPFS哈希 - 支持各种quicknode子域名格式
            pattern = r'quicknode-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}

            return None
        except Exception as e:
            logger.error(f"quicknode-ipfs方法处理失败: {e}")
            return None

    def try_universal_ipfs_method(self, url):
        """方法4：万能方法 - 处理所有包含ipfs的域名"""
        try:
            if 'ipfs' not in url.lower():
                return None

            # 常见的IPFS URL模式
            patterns = [
                r'/ipfs/([a-zA-Z0-9]+)',  # 匹配 /ipfs/hash 格式
                r'ipfs\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 ipfs.io/ipfs/hash
                r'gateway\.ipfs\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 gateway.ipfs.io/ipfs/hash
                r'dweb\.link/ipfs/([a-zA-Z0-9]+)',  # 匹配 dweb.link/ipfs/hash
                r'ipfs\.infura\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 ipfs.infura.io/ipfs/hash
                r'[a-zA-Z0-9\-\.]+\.ipfs\.com/ipfs/([a-zA-Z0-9]+)',  # 匹配各种*.ipfs.com域名
            ]

            for pattern in patterns:
                match = re.search(pattern, url, re.IGNORECASE)
                if match:
                    address = match.group(1)
                    if self.is_valid_ipfs_hash(address):
                        new_url = f"https://ipfs.io/ipfs/{address}"
                        return {'new_url': new_url, 'address': address}

            # 如果正则都不匹配，尝试从URL路径的最后一段提取
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')

            # 寻找可能的IPFS哈希
            for part in reversed(path_parts):  # 从后往前查找
                if self.is_valid_ipfs_hash(part):
                    new_url = f"https://ipfs.io/ipfs/{part}"
                    return {'new_url': new_url, 'address': part}

            return None

        except Exception as e:
            logger.error(f"万能IPFS方法处理失败: {e}")
            return None

    def is_valid_ipfs_hash(self, hash_str):
        """验证是否是有效的IPFS哈希"""
        if not hash_str:
            return False

        # IPFS哈希通常满足以下条件之一：
        # 1. 以Qm开头，长度46个字符（CIDv0）
        # 2. 以b开头，长度较长（CIDv1 base32）
        # 3. 以z开头，长度较长（CIDv1 base58btc）
        # 4. 长度在40-60个字符之间的字母数字组合

        if len(hash_str) < 40:
            return False

        # CIDv0格式：以Qm开头，46个字符
        if hash_str.startswith('Qm') and len(hash_str) == 46:
            return True

        # CIDv1格式：以b或z开头
        if (hash_str.startswith('b') or hash_str.startswith('z')) and len(hash_str) > 45:
            return True

        # 通用验证：长度在合理范围内的字母数字组合
        if 40 <= len(hash_str) <= 65 and hash_str.isalnum():
            return True

        return False

    def _handle_request_failure(self, url, error_msg):
        """处理请求失败"""
        try:
            failure_data = {
                'url': url,
                'error': error_msg,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'retry_count': self.max_retry
            }
            self.redis_client.lpush(self.failed_url_key, json.dumps(failure_data, ensure_ascii=False))
            logger.error(f"请求失败已记录: {url}")
        except Exception as e:
            logger.error(f"记录失败URL时出错: {e}")

    def parse_data(self, response_data):
        """解析响应数据，提取image URL和社交媒体字段"""
        if not response_data:
            return None

        try:
            if hasattr(response_data, 'json'):
                try:
                    data = response_data.json()
                except:
                    return None
            elif isinstance(response_data, dict):
                data = response_data
            else:
                return None

            if 'error' in data:
                return None

            image_url = data.get('image', '')
            if not image_url:
                return None

            # 对image URL也进行IPFS域名转换处理
            if self.is_ipfs_url(image_url):
                logger.info(f"检测到image字段也是IPFS URL，进行转换: {image_url}")
                ipfs_result = self.process_ipfs_url(image_url)
                if ipfs_result:
                    image_url = ipfs_result['new_url']
                    logger.info(f"image URL转换成功: {image_url}")
                else:
                    logger.warning(f"image URL转换失败，使用原URL: {image_url}")

            extra_data = {}

            # 提取社交媒体字段
            if 'twitter' in data and data['twitter']:
                extra_data['twitter'] = data['twitter']
            if 'telegram' in data and data['telegram']:
                extra_data['telegram'] = data['telegram']
            if 'createdOn' in data and data['createdOn']:
                extra_data['createdOn'] = data['createdOn']
            if 'website' in data and data['website']:
                extra_data['website'] = data['website']

            # 从extensions对象提取
            if 'extensions' in data and isinstance(data['extensions'], dict):
                extensions = data['extensions']
                if 'twitter' in extensions and extensions['twitter'] and 'twitter' not in extra_data:
                    extra_data['twitter'] = extensions['twitter']
                if 'telegram' in extensions and extensions['telegram'] and 'telegram' not in extra_data:
                    extra_data['telegram'] = extensions['telegram']
                if 'website' in extensions and extensions['website'] and 'website' not in extra_data:
                    extra_data['website'] = extensions['website']


            # 正则表达式作为保底方法
            if 'twitter' not in extra_data or 'telegram' not in extra_data:
                regex_extracted = self._extract_social_links_with_regex(data)
                if 'twitter' not in extra_data and 'twitter' in regex_extracted:
                    extra_data['twitter'] = regex_extracted['twitter']
                if 'telegram' not in extra_data and 'telegram' in regex_extracted:
                    extra_data['telegram'] = regex_extracted['telegram']
                if 'website' not in extra_data and 'website' in regex_extracted:
                    extra_data['website'] = regex_extracted['website']

            extra_json = json.dumps(extra_data, ensure_ascii=False) if extra_data else None

            return {
                'image_url': image_url,
                'extra_json': extra_json,
                'raw_data': data
            }

        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None

    def _extract_social_links_with_regex(self, data):
        """使用正则表达式提取社交媒体链接"""
        extracted_data = {}
        data_str = json.dumps(data, ensure_ascii=False).lower()

        # Twitter正则
        twitter_patterns = [
            r'https?://(?:www\.)?(?:twitter\.com|x\.com)/[^\s"\'<>]+',
            r'https?://t\.co/[^\s"\'<>]+',
        ]

        # Telegram正则
        telegram_patterns = [
            r'https?://(?:www\.)?t\.me/[^\s"\'<>]+',
            r'https?://(?:www\.)?telegram\.me/[^\s"\'<>]+',
        ]

        # 提取Twitter链接
        for pattern in twitter_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                extracted_data['twitter'] = matches[0]
                break

        # 提取Telegram链接
        for pattern in telegram_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                extracted_data['telegram'] = matches[0]
                break

        return extracted_data

    def download_and_upload_image(self, image_url, address, proxy=None, s3_client=None):
        """下载图片并上传到S3"""
        try:
            if proxy is None:
                proxy = self.get_next_proxy()

            # 如果未提供s3_client，则使用类级别的备用客户端
            if s3_client is None:
                s3_client = self.s3

            retry_count = 0
            image_data = None
            original_image_url = image_url  # 保存原始URL

            while retry_count < self.max_retry:
                try:
                    retry_count += 1
                    response = cf_requests.get(image_url, proxies=proxy, impersonate="chrome116", timeout=self.timeout)
                    response.raise_for_status()
                    image_data = response.content
                    break
                except Exception as e:
                    if retry_count < self.max_retry:
                        time.sleep(0.5)
                    else:
                        # 最后一次重试失败，尝试IPFS域名转换
                        if self.is_ipfs_url(original_image_url):
                            logger.info(f"图片下载失败，尝试IPFS域名转换: {original_image_url}")
                            converted_result = self.process_ipfs_url(original_image_url)

                            if converted_result and converted_result['new_url'] != original_image_url:
                                new_image_url = converted_result['new_url']
                                logger.info(f"图片URL转换成功: {original_image_url} -> {new_image_url}")

                                # 用转换后的URL重新下载
                                try:
                                    response = cf_requests.get(new_image_url, proxies=proxy, impersonate="chrome116",
                                                               timeout=self.timeout)
                                    response.raise_for_status()
                                    image_data = response.content
                                    logger.info(f"转换后的图片URL下载成功: {new_image_url}")
                                    break
                                except Exception as convert_error:
                                    logger.error(
                                        f"转换后的图片URL仍然下载失败: {new_image_url} - 错误: {convert_error}")
                            else:
                                logger.warning(f"图片URL转换失败: {original_image_url}")

                        self._handle_image_download_failure(original_image_url, address, str(e))
                        return None

            if not image_data:
                return None

            # 生成S3对象名称
            s3_object_name = f"logo/101-{address}.png"

            try:
                s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_object_name,
                    Body=image_data,
                    ContentType=self._get_content_type(s3_object_name),
                    ACL='public-read'
                )

                s3_url = f"https://{self.bucket_name}.s3.{self.aws_config['region_name']}.amazonaws.com/{s3_object_name}"
                logger.info(f"图片上传成功: {s3_url}")

                return {
                    's3_url': s3_url,
                    's3_key': s3_object_name,
                    'file_size': len(image_data),
                    'original_url': original_image_url
                }
            except Exception as e:
                logger.error(f"S3上传失败: {e}")
                return None

        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            return None

    def _get_content_type(self, file_path):
        """获取文件Content-Type"""
        if '/' in file_path:
            file_extension = '.' + file_path.split('.')[-1].lower()
        else:
            file_extension = file_path.lower()

        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml'
        }
        return content_types.get(file_extension, 'image/png')

    def _handle_image_download_failure(self, image_url, address, error_msg):
        """处理图片下载失败"""
        try:
            failure_data = {
                'type': 'image_download',
                'image_url': image_url,
                'address': address,
                'error': error_msg,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'retry_count': self.max_retry
            }
            self.redis_client.lpush(self.failed_image_key, json.dumps(failure_data, ensure_ascii=False))
        except Exception as e:
            logger.error(f"记录图片失败时出错: {e}")

    def update_database_icon(self, address, s3_key):
        """更新数据库icon字段"""
        if not s3_key:
            return False

        icon_path = f"/{s3_key}"
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = "UPDATE coin_info SET icon = %s WHERE address = %s"
                cursor.execute(sql, (icon_path, address))
                connection.commit()
                logger.info(f"Icon字段更新成功: {address}")
                return True
        except Exception as e:
            logger.error(f"Icon字段更新失败: {address} - {e}")
            connection.rollback()
            return False
        finally:
            connection.close()

    def update_database_extra(self, address, extra_json):
        """更新数据库extra字段"""
        if not extra_json:
            return False

        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = "UPDATE coin_info SET extra = %s WHERE address = %s"
                cursor.execute(sql, (extra_json, address))
                connection.commit()

                update_data = json.loads(extra_json) if extra_json else {}
                logger.info(f"Extra字段更新成功: {address} - 字段: {list(update_data.keys())}")
                return True
        except Exception as e:
            logger.error(f"Extra字段更新失败: {address} - {e}")
            connection.rollback()
            return False
        finally:
            connection.close()

    def send_result_to_kafka(self, address, icon):
        """发送处理结果到Kafka生产者topic"""
        result_data = {
            'chain_name': 'SOL',
            'address': address,
            'icon': icon
        }

        retry_count = 0
        while retry_count < self.max_retry:
            try:
                retry_count += 1
                future = self.producer.send(self.producer_topic, value=result_data)
                # 等待发送完成
                record_metadata = future.get(timeout=30)

                logger.info(f"结果发送成功到Kafka - Topic: {record_metadata.topic}, "
                            f"分区: {record_metadata.partition}, 偏移量: {record_metadata.offset}")
                logger.info(f"发送内容: {result_data}")
                with self.stats_lock:
                    self.producer_success_count += 1
                return True

            except Exception as e:
                if retry_count < self.max_retry:
                    logger.warning(f"Kafka发送失败 (第{retry_count}次重试): {e}")
                    time.sleep(0.5)
                else:
                    self._handle_producer_failure(result_data, str(e))
                    with self.stats_lock:
                        self.producer_error_count += 1
                    return False
        return False

    def _handle_producer_failure(self, result_data, error_msg):
        """处理生产者发送失败"""
        try:
            failure_data = {
                'type': 'producer_send',
                'result_data': result_data,
                'error': error_msg,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'retry_count': self.max_retry
            }
            self.redis_client.lpush(self.failed_producer_key, json.dumps(failure_data, ensure_ascii=False))
            logger.error(f"生产者发送失败已记录: {result_data}")
        except Exception as e:
            logger.error(f"记录生产者失败时出错: {e}")

    def process_single_message(self, message_data, s3_client=None):
        """处理单条Kafka消息"""
        try:
            # 处理消息格式 - 如果是数组则取第一个元素
            if isinstance(message_data, list) and len(message_data) > 0:
                message_data = message_data[0]
                logger.info(f"处理数组格式消息，提取第一个元素: {message_data}")
            elif isinstance(message_data, list) and len(message_data) == 0:
                logger.warning("收到空数组消息")
                return False

            # 解析消息内容
            address = message_data.get('address')
            uri = message_data.get('uri')

            if not address or not uri:
                logger.warning(f"消息缺少必要字段: {message_data}")
                return False

            logger.info(f"处理新币: {address}")
            logger.info(f"URI: {uri}")

            # 1. 发送请求获取元数据
            response_data = self.make_request(uri)
            if not response_data:
                logger.error(f"获取元数据失败: {address}")
                return False

            # 2. 解析数据
            parsed_data = self.parse_data(response_data)
            if not parsed_data:
                logger.error(f"数据解析失败: {address}")
                return False

            image_url = parsed_data['image_url']
            extra_json = parsed_data['extra_json']

            # 3. 下载图片并上传S3
            s3_info = self.download_and_upload_image(image_url, address, s3_client=s3_client)

            # 4. 更新数据库
            icon_updated = False
            icon_path = None
            if s3_info and s3_info.get('s3_key'):
                icon_path = f"/{s3_info['s3_key']}"
                icon_updated = self.update_database_icon(address, s3_info['s3_key'])

            extra_updated = False
            if extra_json:
                extra_updated = self.update_database_extra(address, extra_json)

            # 5. 所有处理完成后，发送结果到Kafka
            producer_success = False
            if icon_updated and icon_path:
                producer_success = self.send_result_to_kafka(address, icon_path)
            else:
                logger.warning(f"Icon更新失败，跳过Kafka发送: {address}")

            success = icon_updated and producer_success

            logger.info(f"处理完成: {address} - Icon: {icon_updated}, Extra: {extra_updated}, "
                        f"Kafka发送: {producer_success}, 总体成功: {success}")
            return success

        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return False

    def run_consumer(self):
        """运行新架构：1个消费者线程 + 多个处理线程"""
        logger.info(f"启动SOL新币Kafka消费者... (1个消费者线程 + {self.num_threads}个处理线程)")
        self.start_time = datetime.now()

        def signal_handler(signum, frame):
            logger.warning(f"接收到信号 {signum}，正在安全退出...")
            self.is_running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        threads = []

        # 启动1个消费者线程
        consumer_thread = threading.Thread(target=self._kafka_consumer_worker, name="KafkaConsumer")
        threads.append(consumer_thread)
        consumer_thread.start()
        logger.info("Kafka消费者线程已启动")

        # 启动多个处理线程
        for i in range(self.num_threads):
            processor_thread = threading.Thread(target=self._message_processor_worker, name=f"Processor-{i + 1}")
            threads.append(processor_thread)
            processor_thread.start()
            logger.info(f"消息处理线程 Processor-{i + 1} 已启动")

        try:
            while self.is_running:
                alive_threads = [t for t in threads if t.is_alive()]
                if len(alive_threads) < len(threads):
                    logger.error("一个或多个工作线程已意外终止，正在关闭所有线程...")
                    self.is_running = False
                    break

                # 显示队列状态
                queue_size = self.message_queue.qsize()
                self.show_progress(queue_size)
                time.sleep(3)

        except KeyboardInterrupt:
            logger.info("主线程被中断，正在关闭...")
            self.is_running = False

        logger.info("正在等待所有工作线程完成...")
        for thread in threads:
            thread.join()

        if hasattr(self, 'producer'):
            self.producer.close()
            logger.info("Kafka生产者已关闭")

        logger.info("所有线程已停止。")
        self.show_final_statistics()

    def _kafka_consumer_worker(self):
        """Kafka消费者线程：只负责消费消息并放入队列"""
        consumer_config = {
            'bootstrap_servers': self.bootstrap_servers,
            'group_id': self.consumer_group,
            'auto_offset_reset': 'latest',  # 改为latest，从最新消息开始
            'enable_auto_commit': True,
            'auto_commit_interval_ms': 1000,
            'value_deserializer': lambda x: json.loads(x.decode('utf-8')) if x else None,
            'consumer_timeout_ms': 10000,
        }

        try:
            consumer = KafkaConsumer(self.consumer_topic, **consumer_config)
            logger.info(f"✓ Kafka消费者启动成功")
        except Exception as e:
            logger.error(f"Kafka消费者初始化失败: {e}")
            return

        while self.is_running:
            try:
                for message in consumer:
                    if not self.is_running:
                        break

                    # 将消息放入队列供处理线程消费
                    try:
                        self.message_queue.put(message.value, timeout=5)
                        logger.info(f"消息已放入队列 - 分区: {message.partition}, 偏移量: {message.offset}, 队列大小: {self.message_queue.qsize()}")
                    except:
                        logger.warning("消息队列已满，跳过此消息")
                        continue

                if not self.is_running:
                    break

            except Exception as e:
                logger.error(f"Kafka消费者发生意外错误: {e}")
                time.sleep(1)
                continue

        consumer.close()
        logger.info("Kafka消费者已关闭")

    def _message_processor_worker(self):
        """消息处理线程：从队列取消息并处理"""
        # 每个处理线程创建自己的S3客户端
        s3_client = boto3.client('s3', **self.aws_config)
        thread_name = threading.current_thread().name

        logger.info(f"✓ {thread_name} 消息处理线程启动成功")

        while self.is_running:
            try:
                # 从队列获取消息，超时时间1秒
                try:
                    message_data = self.message_queue.get(timeout=1)
                except:
                    continue  # 超时则继续循环

                with self.stats_lock:
                    self.processed_count += 1
                    current_count = self.processed_count

                logger.info(f"({thread_name}) 开始处理消息 #{current_count}")

                # 处理消息
                success = self.process_single_message(message_data, s3_client=s3_client)

                with self.stats_lock:
                    if success:
                        self.success_count += 1
                    else:
                        self.error_count += 1

                # 标记任务完成
                self.message_queue.task_done()

            except Exception as e:
                with self.stats_lock:
                    self.error_count += 1
                logger.error(f"({thread_name}) 处理消息时发生意外错误: {e}")
                continue

        logger.info(f"{thread_name} 消息处理线程已关闭")

    def show_progress(self, queue_size=0):
        """显示处理进度 (线程安全)"""
        if self.start_time:
            with self.stats_lock:
                processed_count = self.processed_count
                success_count = self.success_count
                error_count = self.error_count
                producer_success_count = self.producer_success_count
                producer_error_count = self.producer_error_count

            elapsed = datetime.now() - self.start_time
            rate = processed_count / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0

            logger.info(f"进度统计 - 总数: {processed_count}, 成功: {success_count}, "
                        f"失败: {error_count}, Kafka发送成功: {producer_success_count}, "
                        f"Kafka发送失败: {producer_error_count}, 处理速度: {rate:.2f}/秒, "
                        f"队列大小: {queue_size}")

    def show_final_statistics(self):
        """显示最终统计信息"""
        if self.start_time:
            # 在主线程中调用，此时工作线程已停止，理论上无需加锁，但为了安全起见加上
            with self.stats_lock:
                processed_count = self.processed_count
                success_count = self.success_count
                error_count = self.error_count
                producer_success_count = self.producer_success_count
                producer_error_count = self.producer_error_count

            elapsed = datetime.now() - self.start_time
            logger.info("=" * 60)
            logger.info("最终统计信息:")
            logger.info(f"运行时间: {elapsed}")
            logger.info(f"处理总数: {processed_count}")
            logger.info(f"成功数量: {success_count}")
            logger.info(f"失败数量: {error_count}")
            logger.info(f"Kafka发送成功: {producer_success_count}")
            logger.info(f"Kafka发送失败: {producer_error_count}")
            logger.info(f"成功率: {(success_count / processed_count * 100):.2f}%" if processed_count > 0 else "0%")
            logger.info(
                f"Kafka发送成功率: {(producer_success_count / (producer_success_count + producer_error_count) * 100):.2f}%" if (
                                                                                                                                           producer_success_count + producer_error_count) > 0 else "0%")
            logger.info("=" * 60)

    def test_single_data(self):
        """测试单条数据的完整处理流程"""
        logger.info("=" * 60)
        logger.info("测试模式 - 从Kafka消费一条真实数据")
        logger.info("=" * 60)

        print("正在从Kafka消费一条真实消息进行测试...")
        print(f"消费Topic: {self.consumer_topic}")
        print(f"生产Topic: {self.producer_topic}")
        print("=" * 60)

        self.start_time = datetime.now()

        # 创建消费者配置
        consumer_config = {
            'bootstrap_servers': self.bootstrap_servers,
            'group_id': f'{self.consumer_group}-test',  # 使用不同的组避免影响生产消费者
            'auto_offset_reset': 'latest',  # 从最新消息开始
            'enable_auto_commit': False,  # 测试模式不提交偏移量
            'value_deserializer': lambda x: json.loads(x.decode('utf-8')) if x else None,
            'consumer_timeout_ms': 30000,  # 30秒超时
        }

        try:
            consumer = KafkaConsumer(self.consumer_topic, **consumer_config)
            logger.info("✓ 测试消费者创建成功，等待消息...")

            message_found = False

            # 尝试消费一条消息
            for message in consumer:
                try:
                    logger.info(f"✓ 收到测试消息 - 分区: {message.partition}, 偏移量: {message.offset}")
                    logger.info(f"消息内容: {message.value}")

                    # 处理这条真实消息
                    success = self.process_single_message(message.value)

                    # 更新统计
                    self.processed_count = 1
                    if success:
                        self.success_count = 1
                        logger.info("✅ 测试成功！整个流程完成")
                    else:
                        self.error_count = 1
                        logger.error("❌ 测试失败！请检查错误日志")

                    message_found = True
                    break  # 只处理一条消息

                except Exception as e:
                    logger.error(f"处理测试消息异常: {e}")
                    self.error_count = 1
                    message_found = True
                    break

            if not message_found:
                logger.warning("⚠️  超时未收到消息，请确保Kafka中有数据")
                print(f"\n可能的原因:")
                print(f"1. Topic '{self.consumer_topic}' 中暂时没有新消息")
                print(f"2. Kafka集群连接问题")
                print(f"3. 请向Topic中发送测试数据后重试")
                return False

        except Exception as e:
            logger.error(f"Kafka消费异常: {e}")
            return False
        finally:
            if 'consumer' in locals():
                consumer.close()

        # 显示测试结果
        elapsed = datetime.now() - self.start_time
        print("\n" + "=" * 60)
        print("测试结果统计:")
        print(f"运行时间: {elapsed}")
        print(f"处理状态: {'成功' if self.success_count > 0 else '失败'}")
        print(f"Kafka发送成功: {self.producer_success_count}")
        print(f"Kafka发送失败: {self.producer_error_count}")
        print("=" * 60)

        return self.success_count > 0


def main():
    """主函数"""
    logger.info("SOL新币Kafka消费者 - 生产版本")

    # 使用sys.argv来接收命令行参数，适用于Docker环境
    # 脚本用法: python coin_info_kafuka_old.py [mode] [num_threads]
    # mode (可选): 'production' 或 'test'。默认为 'production'。
    # num_threads (可选): 仅在production模式下有效。默认为 12。

    mode = 'production'
    if len(sys.argv) > 1:
        mode = sys.argv[1]

    try:
        if mode == 'production':
            num_threads = 12
            if len(sys.argv) > 2 and sys.argv[2].isdigit():
                num_threads = int(sys.argv[2])

            processor = SolKafkaProcessor(num_threads=num_threads)
            logger.info(f"启动生产模式... (线程数: {num_threads})")
            processor.run_consumer()

        elif mode == 'test':
            processor = SolKafkaProcessor()
            logger.info("启动测试模式...")
            processor.test_single_data()

        else:
            # Fallback for interactive mode if no valid arguments are provided in a TTY environment
            if sys.stdin.isatty():
                print("\n无效的模式参数。请选择运行模式:")
                print("1. 测试模式")
                print("2. 生产模式 (多线程)")
                choice = input("\n请输入选择 (1 或 2, 默认1): ").strip()

                if choice == '2':
                    num_threads_str = input("请输入线程数 (默认 12): ").strip()
                    num_threads = int(num_threads_str) if num_threads_str.isdigit() else 12
                    processor = SolKafkaProcessor(num_threads=num_threads)
                    logger.info("启动生产模式...")
                    processor.run_consumer()
                else:
                    processor = SolKafkaProcessor()
                    logger.info("启动测试模式...")
                    processor.test_single_data()
            else:
                logger.error(f"无效的运行模式 '{mode}'。请使用 'production' 或 'test'。")
                sys.exit(1)

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        traceback.print_exc()


if __name__ == '__main__':
    main()
